# llava/model/relational_embedding.py

from typing import Tuple
import torch
import torch.nn as nn

class RelationalPositionalEncoding(nn.Module):

    def __init__(self, embedding_dim: int, num_bins_per_axis: int = 5, num_dims: int = 3):

        super().__init__()
        self.num_bins_per_axis = num_bins_per_axis
        self.num_dims = num_dims
        self.total_bins = num_bins_per_axis ** self.num_dims
        self.embedding_dim = embedding_dim

        # 注意：embedding_dim应该等于attention的num_heads
        # 这样输出的形状才能与attention scores匹配
        self.relational_embedding = nn.Embedding(self.total_bins, self.embedding_dim)
        boundaries = torch.linspace(-1.0, 1.0, num_bins_per_axis - 1)
        self.register_buffer('boundaries', boundaries)

    def _calculate_relational_ids(self, coords: torch.Tensor) -> torch.Tensor:
        assert coords.shape[-1] == self.num_dims, \
            f"输入坐标有 {coords.shape[-1]} 个维度, 但模块被配置为处理 {self.num_dims} 个维度。"

        # 确保输入是连续的，避免warning
        coords = coords.contiguous()
        delta_coords = coords.unsqueeze(2) - coords.unsqueeze(1)
        delta_coords = delta_coords.contiguous()

        # 添加数值稳定性检查
        if torch.isnan(delta_coords).any() or torch.isinf(delta_coords).any():
            print(f"Warning: delta_coords contains NaN or Inf, replacing with zeros")
            delta_coords = torch.where(torch.isnan(delta_coords) | torch.isinf(delta_coords),
                                     torch.zeros_like(delta_coords), delta_coords)

        all_bins = []
        for i in range(self.num_dims):
            # 确保boundaries在正确的设备上
            boundaries = self.boundaries.to(delta_coords.device)

            # 使用更安全的bucketize
            bins = torch.bucketize(delta_coords[..., i], boundaries, right=False)

            # 确保bins在有效范围内
            bins = torch.clamp(bins, 0, self.num_bins_per_axis - 1)
            all_bins.append(bins)

        relational_ids = torch.zeros_like(all_bins[0], dtype=torch.long)
        for i in range(self.num_dims):
            # 为了与 x, y, z 的直觉匹配，我们反向遍历维度
            dim_index = self.num_dims - 1 - i
            power = i
            contribution = all_bins[dim_index] * (self.num_bins_per_axis ** power)
            relational_ids += contribution

        # 最终安全检查：确保所有ID都在embedding表范围内
        max_id = self.num_bins_per_axis ** self.num_dims - 1
        relational_ids = torch.clamp(relational_ids, 0, max_id)

        return relational_ids.long()

    def forward(self, coords: torch.Tensor) -> torch.Tensor:
        batch_size, seq_len, coord_dim = coords.shape

        # 检查序列长度，如果太长则使用稀疏版本
        if seq_len > 512:  # 只对极长序列完全禁用
            print(f"Warning: Very long sequence ({seq_len}), disabling relational encoding")
            return torch.zeros(batch_size, self.embedding_dim, seq_len, seq_len,
                             device=coords.device, dtype=coords.dtype)
        elif seq_len > 256:  # 对中等长度序列使用稀疏采样
            print(f"Warning: Long sequence ({seq_len}), using sparse relational encoding")
            # 使用稀疏采样：只计算每隔几个patch的关系
            stride = 2  # 每隔2个patch采样一次
            sparse_coords = coords[:, ::stride, :]  # 稀疏采样坐标
            sparse_seq_len = sparse_coords.shape[1]

            # 计算稀疏的relational encoding
            sparse_relational_ids = self._calculate_relational_ids(sparse_coords)
            sparse_bias_vectors = self.relational_embedding(sparse_relational_ids.to(self.relational_embedding.weight.device))
            sparse_result = sparse_bias_vectors.permute(0, 3, 1, 2)

            # 插值回原始大小
            result = torch.nn.functional.interpolate(
                sparse_result, size=(seq_len, seq_len), mode='bilinear', align_corners=False
            )
            return result

        relational_ids = self._calculate_relational_ids(coords)
        device = self.relational_embedding.weight.device

        # 确保relational_ids在正确的设备上
        relational_ids = relational_ids.to(device)

        # 添加安全检查
        if torch.isnan(relational_ids).any() or torch.isinf(relational_ids).any():
            print(f"Warning: relational_ids contains NaN or Inf, replacing with zeros")
            relational_ids = torch.where(torch.isnan(relational_ids) | torch.isinf(relational_ids),
                                       torch.zeros_like(relational_ids), relational_ids)

        # 执行embedding lookup
        bias_vectors = self.relational_embedding(relational_ids)

        # 检查embedding输出
        if torch.isnan(bias_vectors).any() or torch.isinf(bias_vectors).any():
            print(f"Warning: bias_vectors contains NaN or Inf, replacing with zeros")
            bias_vectors = torch.where(torch.isnan(bias_vectors) | torch.isinf(bias_vectors),
                                     torch.zeros_like(bias_vectors), bias_vectors)

        # bias_vectors shape: [batch, seq_len, seq_len, embedding_dim]
        # 需要转换为: [batch, embedding_dim, seq_len, seq_len]
        result = bias_vectors.permute(0, 3, 1, 2)

        # 添加形状验证（移除调试打印以提高性能）
        batch_size, embedding_dim, seq_len1, seq_len2 = result.shape
        if seq_len1 != seq_len2:
            print(f"Warning: Non-square attention bias matrix: {seq_len1} x {seq_len2}")

        # 检查张量大小是否合理
        total_elements = result.numel()
        if total_elements > 50_000_000:  # 50M elements threshold
            print(f"Warning: Very large relational bias tensor: {total_elements:,} elements")
            print(f"Shape: {result.shape}, consider reducing sequence length or using sparse attention")

        # 最终检查
        if torch.isnan(result).any() or torch.isinf(result).any():
            print(f"Warning: final result contains NaN or Inf, replacing with zeros")
            result = torch.where(torch.isnan(result) | torch.isinf(result),
                               torch.zeros_like(result), result)

        return result