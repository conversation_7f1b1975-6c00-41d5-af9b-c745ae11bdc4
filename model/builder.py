# 文件路径: llava/model/builder.py
# (最终版 - 适用于已修改的 llava_llama.py)

import os
import warnings
import shutil

from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, AutoConfig, BitsAndBytesConfig
import torch
from llava.model import *
from llava.constants import DEFAULT_IMAGE_PATCH_TOKEN, DEFAULT_IM_START_TOKEN, DEFAULT_IM_END_TOKEN

# 注意：我们不再需要从新文件中导入任何东西，因为所有修改都在 llava_llama.py 中完成了。

def load_pretrained_model(model_path, model_base, model_name, load_8bit=False, load_4bit=False, device_map="gpu", device="cuda", use_flash_attn=False, **kwargs):
    kwargs = {"device_map": device_map, **kwargs}

    if device != "cuda":
        kwargs['device_map'] = {"": device}

    if load_8bit:
        kwargs['load_in_8bit'] = True
    elif load_4bit:
        kwargs['load_in_4bit'] = True
        kwargs['quantization_config'] = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type='nf4'
        )
    else:
        kwargs['torch_dtype'] = torch.float16

    if use_flash_attn:
        kwargs['attn_implementation'] = 'flash_attention_2'

    if 'llava' in model_name.lower():
        # Load LLaVA model
        if 'lora' in model_name.lower() and model_base is None:
            warnings.warn('There is `lora` in model name but no `model_base` is provided. If you are loading a LoRA model, please provide the `model_base` argument. Detailed instruction: https://github.com/haotian-liu/LLaVA#launch-a-model-worker-lora-weights-unmerged.')
        if 'lora' in model_name.lower() and model_base is not None:
            # 导入原始的LlavaConfig和LlavaLlamaForCausalLM
            from llava.model.language_model.llava_llama import LlavaConfig, LlavaLlamaForCausalLM
            lora_cfg_pretrained = LlavaConfig.from_pretrained(model_path)
            tokenizer = AutoTokenizer.from_pretrained(model_base, use_fast=False)
            print('Loading LLaVA from base model...')
            model = LlavaLlamaForCausalLM.from_pretrained(model_base, low_cpu_mem_usage=True, config=lora_cfg_pretrained, **kwargs)
            token_num, tokem_dim = model.lm_head.out_features, model.lm_head.in_features
            if model.lm_head.weight.shape[0] != token_num:
                model.lm_head.weight = torch.nn.Parameter(torch.empty(token_num, tokem_dim, device=model.device, dtype=model.dtype))
                model.model.embed_tokens.weight = torch.nn.Parameter(torch.empty(token_num, tokem_dim, device=model.device, dtype=model.dtype))

            print('Loading additional LLaVA weights...')
            if os.path.exists(os.path.join(model_path, 'non_lora_trainables.bin')):
                non_lora_trainables = torch.load(os.path.join(model_path, 'non_lora_trainables.bin'), map_location='cpu')
            else:
                from huggingface_hub import hf_hub_download
                def load_from_hf(repo_id, filename, subfolder=None):
                    cache_file = hf_hub_download(repo_id=repo_id, filename=filename, subfolder=subfolder)
                    return torch.load(cache_file, map_location='cpu')
                non_lora_trainables = load_from_hf(model_path, 'non_lora_trainables.bin')
            non_lora_trainables = {(k[11:] if k.startswith('base_model.') else k): v for k, v in non_lora_trainables.items()}
            if any(k.startswith('model.model.') for k in non_lora_trainables):
                non_lora_trainables = {(k[6:] if k.startswith('model.') else k): v for k, v in non_lora_trainables.items()}
            model.load_state_dict(non_lora_trainables, strict=False)

            from peft import PeftModel
            print('Loading LoRA weights...')
            model = PeftModel.from_pretrained(model, model_path)
            print('Merging LoRA weights...')

            # 保存关键权重以防合并过程中损坏
            try:
                original_embed_weights = model.get_input_embeddings().weight.clone()
                original_lm_head_weights = model.get_output_embeddings().weight.clone()
                print('✅ Saved original weights before merge')
            except Exception as e:
                print(f'⚠️  Could not save original weights: {e}')
                original_embed_weights = None
                original_lm_head_weights = None

            model = model.merge_and_unload()

            # 检查合并后的权重是否损坏
            embed_corrupted = model.get_input_embeddings().weight.std().item() < 1e-6
            lm_head_corrupted = model.get_output_embeddings().weight.std().item() < 1e-6

            if embed_corrupted or lm_head_corrupted:
                print('⚠️  Detected corrupted weights after merge! Loading from base model...')

                try:
                    from transformers import AutoModelForCausalLM
                    base_model = AutoModelForCausalLM.from_pretrained(
                        model_base,
                        torch_dtype=torch.float16,
                        device_map="auto"
                    )

                    model_vocab_size = model.get_input_embeddings().weight.shape[0]
                    base_vocab_size = base_model.get_input_embeddings().weight.shape[0]

                    print(f'   Model vocab: {model_vocab_size}, Base vocab: {base_vocab_size}')

                    if embed_corrupted:
                        print('🔧 Fixing embeddings from base model...')
                        if model_vocab_size != base_vocab_size:
                            min_vocab_size = min(model_vocab_size, base_vocab_size)
                            with torch.no_grad():
                                model.get_input_embeddings().weight[:min_vocab_size].copy_(
                                    base_model.get_input_embeddings().weight[:min_vocab_size]
                                )
                                if model_vocab_size > base_vocab_size:
                                    mean_embedding = base_model.get_input_embeddings().weight.mean(dim=0, keepdim=True)
                                    model.get_input_embeddings().weight[base_vocab_size:].copy_(
                                        mean_embedding.expand(model_vocab_size - base_vocab_size, -1)
                                    )
                        else:
                            with torch.no_grad():
                                model.get_input_embeddings().weight.copy_(base_model.get_input_embeddings().weight)

                    if lm_head_corrupted:
                        print('🔧 Fixing lm_head from base model...')
                        if model_vocab_size != base_vocab_size:
                            min_vocab_size = min(model_vocab_size, base_vocab_size)
                            with torch.no_grad():
                                model.get_output_embeddings().weight[:min_vocab_size].copy_(
                                    base_model.get_output_embeddings().weight[:min_vocab_size]
                                )
                                if model_vocab_size > base_vocab_size:
                                    mean_lm_head = base_model.get_output_embeddings().weight.mean(dim=0, keepdim=True)
                                    model.get_output_embeddings().weight[base_vocab_size:].copy_(
                                        mean_lm_head.expand(model_vocab_size - base_vocab_size, -1)
                                    )
                        else:
                            with torch.no_grad():
                                model.get_output_embeddings().weight.copy_(base_model.get_output_embeddings().weight)

                    print('✅ Successfully fixed weights from base model!')
                    del base_model
                    torch.cuda.empty_cache()

                except Exception as e:
                    print(f'❌ Failed to fix from base model: {e}')
                    # Fallback to original backup if available
                    if embed_corrupted and original_embed_weights is not None:
                        print('🔧 Fallback: Restoring original embeddings...')
                        with torch.no_grad():
                            model.get_input_embeddings().weight.copy_(original_embed_weights)

                    if lm_head_corrupted and original_lm_head_weights is not None:
                        print('🔧 Fallback: Restoring original lm_head...')
                        with torch.no_grad():
                            model.get_output_embeddings().weight.copy_(original_lm_head_weights)

            print('Model is loaded...')
        elif model_base is not None:
            # this may be mm projector only
            print('Loading LLaVA from base model...')
            if 'mpt' in model_name.lower():
                # ... (mpt 相关的代码保持不变) ...
                tokenizer = AutoTokenizer.from_pretrained(model_base, use_fast=True)
                cfg_pretrained = AutoConfig.from_pretrained(model_path, trust_remote_code=True)
                model = LlavaMptForCausalLM.from_pretrained(model_base, low_cpu_mem_usage=True, config=cfg_pretrained, **kwargs)
            else:
                from llava.model.language_model.llava_llama import LlavaConfig, LlavaLlamaForCausalLM
                tokenizer = AutoTokenizer.from_pretrained(model_base, use_fast=False)
                cfg_pretrained = LlavaConfig.from_pretrained(model_path)
                model = LlavaLlamaForCausalLM.from_pretrained(model_base, low_cpu_mem_usage=True, config=cfg_pretrained, **kwargs)

            mm_projector_weights = torch.load(os.path.join(model_path, 'mm_projector.bin'), map_location='cpu')
            mm_projector_weights = {k: v.to(torch.float16) for k, v in mm_projector_weights.items()}
            model.load_state_dict(mm_projector_weights, strict=False)
        else:
            # --- 核心修改：我们通过检查config文件中的新参数来决定加载哪个模型 ---
            # 首先导入我们可能需要的所有 LLaVA 模型
            from llava.model.language_model.llava_llama import LlavaLlamaForCausalLM
            from llava.model.language_model.llava_mpt import LlavaMptForCausalLM
            from llava.model.language_model.llava_mistral import LlavaMistralForCausalLM

            cfg_pretrained = AutoConfig.from_pretrained(model_path)
            
            # 检查我们自定义的开关
            if getattr(cfg_pretrained, 'use_dual_stream_encoding', False):
                print("==================================================")
                print("✅ Activating CUSTOM DUAL STREAM LLaVA MODEL...")
                print("==================================================")
                tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
                # 注意：这里我们实例化的仍然是 LlavaLlamaForCausalLM，
                # 因为我们已经直接在那个文件里修改了它！
                # 该文件中的 config 和 model 类都已经被我们增强
                model = LlavaLlamaForCausalLM.from_pretrained(
                    model_path,
                    low_cpu_mem_usage=True,
                    **kwargs
                )
            # --- 修改结束 ---
            elif 'mpt' in model_name.lower():
                tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=True)
                model = LlavaMptForCausalLM.from_pretrained(model_path, low_cpu_mem_usage=True, **kwargs)
            elif 'mistral' in model_name.lower():
                tokenizer = AutoTokenizer.from_pretrained(model_path)
                model = LlavaMistralForCausalLM.from_pretrained(
                    model_path,
                    low_cpu_mem_usage=True,
                    **kwargs
                )
            else:
                # 如果config中没有特殊标记，就加载原始模型
                tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
                model = LlavaLlamaForCausalLM.from_pretrained(
                    model_path,
                    low_cpu_mem_usage=True,
                    **kwargs
                )
    else:
        # Load language model
        if model_base is not None:
            # PEFT model
            from peft import PeftModel
            tokenizer = AutoTokenizer.from_pretrained(model_base, use_fast=False)
            model = AutoModelForCausalLM.from_pretrained(model_base, low_cpu_mem_usage=True, **kwargs)
            print(f"Loading LoRA weights from {model_path}")
            model = PeftModel.from_pretrained(model, model_path)
            print(f"Merging weights")
            model = model.merge_and_unload()
            print('Convert to FP16...')
            model.to(torch.float16)
        else:
            use_fast = False
            if 'mpt' in model_name.lower():
                tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=True)
                model = AutoModelForCausalLM.from_pretrained(model_path, low_cpu_mem_usage=True, trust_remote_code=True, **kwargs)
            else:
                tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
                model = AutoModelForCausalLM.from_pretrained(model_path, low_cpu_mem_usage=True, **kwargs)

    image_processor = None

    if 'llava' in model_name.lower():
        mm_use_im_start_end = getattr(model.config, "mm_use_im_start_end", False)
        mm_use_im_patch_token = getattr(model.config, "mm_use_im_patch_token", True)
        if mm_use_im_patch_token:
            tokenizer.add_tokens([DEFAULT_IMAGE_PATCH_TOKEN], special_tokens=True)
        if mm_use_im_start_end:
            tokenizer.add_tokens([DEFAULT_IM_START_TOKEN, DEFAULT_IM_END_TOKEN], special_tokens=True)
        model.resize_token_embeddings(len(tokenizer))

        vision_tower = model.get_vision_tower()
        if not vision_tower.is_loaded:
            vision_tower.load_model(device_map=device_map)
        if device_map != 'auto':
            vision_tower.to(device=device_map, dtype=kwargs.get('torch_dtype', torch.float16))
        if hasattr(model, 'lm_head') and 'bitsandbytes' in str(type(model.get_output_embeddings())):
            print("Warning: Detected a bitsandbytes quantized lm_head.")
            print("Temporarily replacing it with a float32 layer to avoid resize errors.")
            old_lm_head = model.get_output_embeddings()
            in_features = old_lm_head.in_features
            out_features = old_lm_head.out_features
            device = old_lm_head.weight.device
            new_lm_head = torch.nn.Linear(
                in_features,
                out_features,
                bias=False,
                device=device,
                dtype=torch.float16 # Use float32 for stability
            )
            
            model.set_output_embeddings(new_lm_head)
            print("Successfully replaced lm_head for safe resizing.")
        image_processor = vision_tower.image_processor
            
    if hasattr(model.config, "max_sequence_length"):
        context_len = model.config.max_sequence_length
    else:
        context_len = 2048

    return tokenizer, model, image_processor, context_len