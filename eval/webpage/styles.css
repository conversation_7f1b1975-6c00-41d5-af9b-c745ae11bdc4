body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-dark .navbar-nav .nav-link {
    color: #f1cf68;
    font-size: 1.1rem;
    padding: 0.5rem 0.6rem;
}

.card-header {
    font-weight: bold;
}

.card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: 0.3s;
}

.card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

button {
    transition: background-color 0.3s;
}

button:hover {
    background-color: #007bff;
}

@media (max-width: 767px) {
    .form-row .form-group {
        margin-bottom: 10px;
    }
}

/* Extra styles */

.expandable-card .card-text-container {
    max-height: 200px;
    overflow-y: hidden;
    position: relative;
}

.expandable-card.expanded .card-text-container {
    max-height: none;
}

.expand-btn {
    position: relative;
    display: none;
    background-color: rgba(255, 255, 255, 0.8);
    color: #510c75;
    border-color: transparent;
}

.expand-btn:hover {
    background-color: rgba(200, 200, 200, 0.8);
    text-decoration: none;
    border-color: transparent;
    color: #510c75;
}

.expand-btn:focus {
    outline: none;
    text-decoration: none;
}

.expandable-card:not(.expanded) .card-text-container:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 90px;
    background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 1));
}

.expandable-card:not(.expanded) .expand-btn {
    margin-top: -40px;
}

.card-body {
    padding-bottom: 5px;
}

.vertical-flex-layout {
    justify-content: center;
    align-items: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.figure-img {
    max-width: 100%;
    height: auto;
}

.adjustable-font-size {
    font-size: calc(0.5rem + 2vw);
}
