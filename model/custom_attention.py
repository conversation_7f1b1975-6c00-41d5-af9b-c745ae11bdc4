# 文件路径: llava/model/custom_attention.py

import torch
import torch.nn as nn
from typing import Optional
class RelationalAttention(nn.Module):
    def __init__(self, hidden_size: int, num_heads: int):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.head_dim = hidden_size // num_heads

        if self.head_dim * self.num_heads != self.hidden_size:
            raise ValueError("hidden_size must be divisible by num_heads")

        # 定义Q, K, V的线性投影层
        self.q_proj = nn.Linear(hidden_size, hidden_size)
        self.k_proj = nn.Linear(hidden_size, hidden_size)
        self.v_proj = nn.Linear(hidden_size, hidden_size)
        
        # 定义输出投影层
        self.out_proj = nn.Linear(hidden_size, hidden_size)

    def _shape(self, tensor: torch.Tensor, seq_len: int, bsz: int):
        return tensor.view(bsz, seq_len, self.num_heads, self.head_dim).transpose(1, 2).contiguous()

    def forward(
        self,
        hidden_states: torch.Tensor,
        relational_bias: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        
        bsz, seq_len, _ = hidden_states.shape

        query_states = self._shape(self.q_proj(hidden_states), seq_len, bsz)
        key_states = self._shape(self.k_proj(hidden_states), seq_len, bsz)
        value_states = self._shape(self.v_proj(hidden_states), seq_len, bsz)

        # 计算attention scores with numerical stability
        attn_scores = torch.matmul(query_states, key_states.transpose(-1, -2))

        # 添加数值稳定性检查
        if torch.isnan(attn_scores).any() or torch.isinf(attn_scores).any():
            print("Warning: NaN/Inf in raw attention scores, using zeros")
            attn_scores = torch.zeros_like(attn_scores)

        # 安全的缩放
        scale_factor = (self.head_dim ** 0.5)
        attn_scores = attn_scores / scale_factor

        # 裁剪极值以防止softmax溢出
        attn_scores = torch.clamp(attn_scores, min=-50.0, max=50.0)

        if relational_bias is not None:
            # 确保relational_bias的形状与attn_scores匹配
            # relational_bias: [batch, embedding_dim, seq_len, seq_len]
            # attn_scores: [batch, num_heads, seq_len, seq_len]

            # 检查维度是否匹配
            if relational_bias.shape[1] != self.num_heads:
                # 如果embedding_dim != num_heads，需要调整
                if relational_bias.shape[1] == 1:
                    # 如果embedding_dim=1，扩展到num_heads
                    relational_bias = relational_bias.expand(-1, self.num_heads, -1, -1)
                else:
                    # 如果embedding_dim > num_heads，取前num_heads个
                    relational_bias = relational_bias[:, :self.num_heads, :, :]

            # 确保形状完全匹配
            if relational_bias.shape != attn_scores.shape:
                print(f"Warning: relational_bias shape {relational_bias.shape} != attn_scores shape {attn_scores.shape}")
                # 作为安全措施，如果形状仍然不匹配，跳过relational bias
                relational_bias = None

            if relational_bias is not None:
                # 检查NaN
                if torch.isnan(relational_bias).any():
                    print("Warning: relational_bias contains NaN, skipping")
                    relational_bias = None
                else:
                    attn_scores = attn_scores + relational_bias
        # 4. 安全的 softmax 计算
        try:
            # 最终检查attention scores
            if torch.isnan(attn_scores).any() or torch.isinf(attn_scores).any():
                print("Warning: NaN/Inf in final attention scores, using uniform attention")
                attn_weights = torch.ones_like(attn_scores) / attn_scores.size(-1)
            else:
                # 使用数值稳定的softmax
                attn_weights = nn.functional.softmax(attn_scores, dim=-1, dtype=torch.float32).to(query_states.dtype)

                # 检查softmax输出
                if torch.isnan(attn_weights).any() or torch.isinf(attn_weights).any():
                    print("Warning: NaN/Inf in attention weights, using uniform attention")
                    attn_weights = torch.ones_like(attn_scores) / attn_scores.size(-1)

            attn_output = torch.matmul(attn_weights, value_states)

            # 检查输出
            if torch.isnan(attn_output).any() or torch.isinf(attn_output).any():
                print("Warning: NaN/Inf in attention output, using zeros")
                attn_output = torch.zeros_like(attn_output)

        except Exception as e:
            print(f"Warning: Attention computation failed ({e}), using zeros")
            attn_output = torch.zeros(bsz, self.num_heads, seq_len, self.head_dim,
                                    device=query_states.device, dtype=query_states.dtype)

        # 还原形状并进行输出投影
        attn_output = attn_output.transpose(1, 2).contiguous().view(bsz, seq_len, self.hidden_size)

        # 安全的输出投影
        try:
            attn_output = self.out_proj(attn_output)

            # 最终输出检查
            if torch.isnan(attn_output).any() or torch.isinf(attn_output).any():
                print("Warning: NaN/Inf in final attention output, using zeros")
                attn_output = torch.zeros_like(attn_output)

        except Exception as e:
            print(f"Warning: Output projection failed ({e}), using zeros")
            attn_output = torch.zeros(bsz, seq_len, self.hidden_size,
                                    device=query_states.device, dtype=query_states.dtype)
        
        return attn_output