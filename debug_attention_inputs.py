#!/usr/bin/env python3
"""
深入调试attention层的输入，找出NaN的真正来源
"""

import os
import torch
import argparse
from transformers import AutoTokenizer
from llava.model.builder import load_pretrained_model
from llava.utils import disable_torch_init
from llava.mm_utils import get_model_name_from_path, process_images, tokenizer_image_token
from llava.constants import IMAGE_TOKEN_INDEX, DEFAULT_IMAGE_TOKEN
from llava.conversation import conv_templates
from PIL import Image

def debug_attention_inputs(model_path, model_base=None):
    """深入调试attention层的输入"""
    
    print("🔬 Deep Attention Input Debug")
    print("=" * 50)
    
    # Setup
    disable_torch_init()
    model_path = os.path.expanduser(model_path)
    model_name = get_model_name_from_path(model_path)
    
    try:
        # Load model
        print("🚀 Loading model...")
        tokenizer, model, image_processor, context_len = load_pretrained_model(
            model_path, 
            model_base, 
            model_name, 
            device_map="auto",
            torch_dtype=torch.float16,
            load_4bit=False
        )
        print("✅ Model loaded successfully!")
        
        # Prepare test input
        dummy_image = Image.new('RGB', (224, 224), color='red')
        images_tensor = process_images([dummy_image], image_processor, model.config)[0]
        images = images_tensor.unsqueeze(0).to(model.device, dtype=torch.float16)
        image_sizes = [dummy_image.size]
        
        prompt = "What do you see in this image?"
        if getattr(model.config, 'mm_use_im_start_end', False):
            prompt = DEFAULT_IMAGE_TOKEN + '\n' + prompt
        else:
            prompt = DEFAULT_IMAGE_TOKEN + '\n' + prompt
        
        conv = conv_templates['llava_v1'].copy()
        conv.append_message(conv.roles[0], prompt)
        conv.append_message(conv.roles[1], None)
        full_prompt = conv.get_prompt()
        
        input_ids = tokenizer_image_token(full_prompt, tokenizer, IMAGE_TOKEN_INDEX, return_tensors='pt').unsqueeze(0).to(model.device)
        
        print(f"📝 Input prepared:")
        print(f"   Input IDs shape: {input_ids.shape}")
        print(f"   Images shape: {images.shape}")
        print()
        
        # Hook to capture inputs at multiple stages
        captured_data = {}

        def create_capture_hook(stage_name):
            def capture_hook(module, input, output):
                try:
                    if isinstance(output, torch.Tensor):
                        tensor = output
                    elif isinstance(output, (tuple, list)) and len(output) > 0:
                        tensor = output[0] if isinstance(output[0], torch.Tensor) else None
                    else:
                        tensor = None

                    if tensor is not None:
                        captured_data[stage_name] = {
                            'shape': tensor.shape,
                            'has_nan': torch.isnan(tensor).any().item(),
                            'has_inf': torch.isinf(tensor).any().item(),
                            'min': tensor.min().item() if tensor.numel() > 0 else 0,
                            'max': tensor.max().item() if tensor.numel() > 0 else 0,
                            'mean': tensor.mean().item() if tensor.numel() > 0 else 0,
                            'std': tensor.std().item() if tensor.numel() > 0 else 0,
                        }
                        print(f"   📊 Captured {stage_name}: shape={tensor.shape}, NaN={torch.isnan(tensor).any().item()}")
                except Exception as e:
                    print(f"   ❌ Error capturing {stage_name}: {e}")
            return capture_hook

        # Hook multiple stages
        hooks = []

        # 1. Hook embedding layer
        if hasattr(model, 'get_model'):
            base_model = model.get_model()
            if hasattr(base_model, 'embed_tokens'):
                hook = base_model.embed_tokens.register_forward_hook(create_capture_hook("embeddings"))
                hooks.append(hook)
                print("✅ Hooked embedding layer")

        # 2. Hook vision tower
        if hasattr(model, 'get_vision_tower'):
            vision_tower = model.get_vision_tower()
            if vision_tower is not None:
                hook = vision_tower.register_forward_hook(create_capture_hook("vision_tower"))
                hooks.append(hook)
                print("✅ Hooked vision tower")

        # 3. Hook mm_projector
        if hasattr(model, 'get_model'):
            base_model = model.get_model()
            if hasattr(base_model, 'mm_projector'):
                hook = base_model.mm_projector.register_forward_hook(create_capture_hook("mm_projector"))
                hooks.append(hook)
                print("✅ Hooked mm_projector")

        # 4. Hook first transformer layer (before attention)
        if hasattr(model, 'get_model'):
            base_model = model.get_model()
            if hasattr(base_model, 'layers') and len(base_model.layers) > 0:
                first_layer = base_model.layers[0]
                hook = first_layer.register_forward_hook(create_capture_hook("first_transformer"))
                hooks.append(hook)
                print("✅ Hooked first transformer layer")

                # Also hook the attention specifically
                if hasattr(first_layer, 'self_attn'):
                    hook = first_layer.self_attn.register_forward_hook(create_capture_hook("first_attention"))
                    hooks.append(hook)
                    print("✅ Hooked first attention layer")
        
        # Run forward pass
        print("🧪 Running forward pass to capture attention inputs...")
        try:
            with torch.no_grad():
                outputs = model(input_ids, images=images, image_sizes=image_sizes)
                logits = outputs.logits
            
            # Analyze captured data
            print("\n📊 Multi-Stage Analysis:")
            if captured_data:
                for stage_name, data in captured_data.items():
                    print(f"   {stage_name}:")
                    print(f"      Shape: {data['shape']}")
                    print(f"      Has NaN: {data['has_nan']}")
                    print(f"      Has Inf: {data['has_inf']}")
                    print(f"      Range: [{data['min']:.6f}, {data['max']:.6f}]")
                    print(f"      Mean: {data['mean']:.6f}")
                    print(f"      Std: {data['std']:.6f}")

                    # Check for problematic values
                    if data['has_nan']:
                        print(f"      ❌ CONTAINS NaN! <-- PROBLEM FOUND!")
                    elif data['has_inf']:
                        print(f"      ❌ CONTAINS Inf! <-- PROBLEM FOUND!")
                    elif abs(data['max']) > 100 or abs(data['min']) > 100:
                        print(f"      ⚠️  HAS EXTREME VALUES!")
                    elif data['std'] < 1e-6:
                        print(f"      ⚠️  HAS VERY LOW VARIANCE!")
                    else:
                        print(f"      ✅ Appears normal")
                    print()
            else:
                print("   ❌ No data captured from any stage!")
            
            # Check final logits
            if torch.isnan(logits).any():
                nan_count = torch.isnan(logits).sum().item()
                total_count = logits.numel()
                print(f"📈 Final logits: {nan_count}/{total_count} NaN ({100*nan_count/total_count:.1f}%)")
            else:
                print(f"📈 Final logits: Clean! Range [{logits.min().item():.4f}, {logits.max().item():.4f}]")
                
        except Exception as e:
            print(f"❌ Error during forward pass: {e}")
            import traceback
            traceback.print_exc()
        
        # Clean up hooks
        for hook in hooks:
            hook.remove()
        
        # Additional analysis: Check model weights
        print("\n🔍 Checking attention layer weights...")
        if hasattr(model, 'get_model'):
            base_model = model.get_model()
            if hasattr(base_model, 'layers') and len(base_model.layers) > 0:
                first_attention = base_model.layers[0].self_attn
                
                # Check query, key, value weights
                for name, param in first_attention.named_parameters():
                    if param is not None:
                        has_nan = torch.isnan(param).any().item()
                        has_inf = torch.isinf(param).any().item()
                        param_std = param.std().item()
                        param_mean = param.mean().item()
                        
                        print(f"   {name}:")
                        print(f"      Shape: {param.shape}")
                        print(f"      Has NaN: {has_nan}")
                        print(f"      Has Inf: {has_inf}")
                        print(f"      Mean: {param_mean:.6f}")
                        print(f"      Std: {param_std:.6f}")
                        
                        if has_nan or has_inf:
                            print(f"      ❌ WEIGHT PROBLEM DETECTED!")
                        elif param_std < 1e-8:
                            print(f"      ⚠️  Very low weight variance!")
                        else:
                            print(f"      ✅ Weight appears normal")
                        print()
        
        print("\n🎉 Deep attention debugging completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    parser = argparse.ArgumentParser(description="Deep attention input debugging")
    parser.add_argument("--model-path", type=str, required=True, help="Path to the model")
    parser.add_argument("--model-base", type=str, default=None, help="Path to the base model (for LoRA)")
    args = parser.parse_args()
    
    debug_attention_inputs(args.model_path, args.model_base)

if __name__ == "__main__":
    main()
